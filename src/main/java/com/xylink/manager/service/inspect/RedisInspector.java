package com.xylink.manager.service.inspect;

import com.xylink.manager.controller.dto.inspect.InspectionTaskMiddlewareDTO;
import com.xylink.manager.controller.dto.inspect.RedisConfigDTO;
import com.xylink.manager.model.em.InspectLadderEnum;
import com.xylink.manager.model.em.InspectionMetricKeyEnum;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.InspectJDBCUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.xylink.manager.service.redis.UnifiedRedisService;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/8 16:15
 */
@Component
@Slf4j
public class RedisInspector {

    @Value("${inspect.redis.single.username:otierdsor}")
    private String redisUser;
    @Value("${inspect.redis.single.password:root*YDW5NwkDt^wTWbQmZkyxW76cV}")
    private String redisPass;
    @Value("${inspect.redis.single.port:6379}")
    private Integer port;

    @Value("${inspect.redis.sentinel.host}")
    private String redisSentinelHost;
    @Value("${inspect.redis.sentinel.password:S4FYhwPSLJk!7YwdfH2JB#wY25Bjsu}")
    private String redisSentinelPasswd;
    @Value("${inspect.redis.sentinel.name}")
    private String redisSentinelName;

    @Value("${inspect.redis.mode:single}")
    private String mode;

    @Autowired
    private K8sService k8sService;
    @Autowired
    private InspectJDBCUtils inspectJDBCUtils;

    @Autowired
    private UnifiedRedisService unifiedRedisService;

    private RedisConfigDTO getRedisConfig() {
        String redisIp = k8sService.getRedisNodeIp();
        String name = "main-redis";
        return RedisConfigDTO.builder().host(redisIp).name(name).user(redisUser).pwd(redisPass).port(port)
                .redisSentinelPasswd(redisSentinelPasswd).redisSentinelName(redisSentinelName).mode(mode).build();
    }

    public boolean inspect(Long taskId) {
        try {
            RedisConfigDTO configDTO = getRedisConfig();
            log.info("[inspection] redis name {}, redis host {}", configDTO.getName(), configDTO.getHost());
            return inspectRedis(taskId, configDTO);
        } catch (Exception e) {
            log.error("[inspection] redis inspect error", e);
        }
        return false;
    }

    private boolean inspectRedis(Long subTaskId, RedisConfigDTO configDTO) {
        boolean valid = false;
        String item = configDTO.getName();
        List<InspectionTaskMiddlewareDTO> metricTasks = new ArrayList<>();
        try {
            Map<String, String> redisInfo = getRedisInfo(configDTO);
            if (redisInfo == null || redisInfo.isEmpty()) {
                saveFailedMetricTasks(subTaskId, metricTasks,item);
            } else {
                valid = true;
                inspectionMetric(subTaskId, redisInfo, metricTasks,item);
            }
        } catch (Exception e) {
            log.error("[inspection] " + configDTO.getName() + " connection error, redis host " + configDTO.getHost(), e);
            saveFailedMetricTasks(subTaskId, metricTasks, item);
        }
        inspectJDBCUtils.insertInspectTaskMiddleware(metricTasks);
        return valid;
    }

    private void inspectionMetric(Long subTaskId, Map<String, String> redisInfo, List<InspectionTaskMiddlewareDTO> metricTasks,String item) {
        metricTasks.add(agentLive(subTaskId, redisInfo,item));
        metricTasks.add(connectedClients(subTaskId, redisInfo,item));
        metricTasks.add(memoryUsed(subTaskId, redisInfo,item));
        metricTasks.add(aof(subTaskId, redisInfo,item));
        metricTasks.add(qps(subTaskId, redisInfo,item));
        metricTasks.add(keySpace(subTaskId, redisInfo,item));
    }

    private Map<String, String> getRedisInfo(RedisConfigDTO configDTO) {
        try {
            // 使用统一的Redis服务获取信息
            return unifiedRedisService.getRedisInfo(configDTO);
        } catch (Exception e) {
            log.error("获取Redis信息失败: {}", configDTO.getHost(), e);
            return new HashMap<>();
        }
    }


    /**
     * redis.info()
     * <p>
     * # Server
     * redis_version:6.0.16
     * redis_git_sha1:00000000
     * redis_git_dirty:0
     * <p>
     * # Memory
     * used_memory:450377360
     * used_memory_human:429.51M
     * used_memory_rss:649330688
     * used_memory_rss_human:619.25M
     * used_memory_peak:1838202392
     * used_memory_peak_human:1.71G
     * used_memory_peak_perc:24.50%
     * used_memory_overhead:76969608
     * used_memory_startup:1966944
     * <p>
     * # Keyspace
     * db0:keys=458274,expires=382762,avg_ttl=18603956947
     * db2:keys=91,expires=90,avg_ttl=39874950
     * db3:keys=424,expires=360,avg_ttl=1299966310
     * db4:keys=25802,expires=11174,avg_ttl=198581088
     * db7:keys=11,expires=0,avg_ttl=0
     * db8:keys=980,expires=649,avg_ttl=12050840
     * db10:keys=1740,expires=116,avg_ttl=169029474
     *
     * @param jedis .
     * @return .
     */


    /**
     * 指标获取失败时，拼接所有指标获取失败的结果
     */
    private void saveFailedMetricTasks(Long subTaskId, List<InspectionTaskMiddlewareDTO> metricTasks, String item) {
        metricTasks.add(new InspectionTaskMiddlewareDTO(subTaskId, item, InspectionMetricKeyEnum.REDIS_AGENT_LIVE.getMetricKey(), "redis连接失败", InspectLadderEnum.EXCEPT.getValue()));
        metricTasks.add(new InspectionTaskMiddlewareDTO(subTaskId, item, InspectionMetricKeyEnum.REDIS_CONNECTION_COUNT.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue()));
        metricTasks.add(new InspectionTaskMiddlewareDTO(subTaskId, item, InspectionMetricKeyEnum.REDIS_ACTUAL_MEMORY_USAGE.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue()));
        metricTasks.add(new InspectionTaskMiddlewareDTO(subTaskId, item, InspectionMetricKeyEnum.REDIS_AOF.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue()));
        metricTasks.add(new InspectionTaskMiddlewareDTO(subTaskId, item, InspectionMetricKeyEnum.REDIS_QPS.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue()));
        metricTasks.add(new InspectionTaskMiddlewareDTO(subTaskId, item, InspectionMetricKeyEnum.REDIS_KEYSPACE.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue()));
    }

    public InspectionTaskMiddlewareDTO agentLive(Long subTaskId, Map<String, String> infoMap,String item) {
        String metricKey = InspectionMetricKeyEnum.REDIS_AGENT_LIVE.getMetricKey();
        if (infoMap == null || infoMap.isEmpty()) {
            return new InspectionTaskMiddlewareDTO(subTaskId,item, metricKey, "连接redis失败", InspectLadderEnum.EXCEPT.getValue());
        }
        return new InspectionTaskMiddlewareDTO(subTaskId,item, metricKey, "连接redis成功", InspectLadderEnum.NORMAL.getValue());
    }

    /**
     * 获取 redis 连接数
     *
     * @param subTaskId .
     * @param infoMap   redisInfo
     * @return metricTask
     */
    public InspectionTaskMiddlewareDTO connectedClients(Long subTaskId, Map<String, String> infoMap,String item) {
        String metricKey = InspectionMetricKeyEnum.REDIS_CONNECTION_COUNT.getMetricKey();
        String maxclients = infoMap.get("maxclients");
        String connectedClients = infoMap.get("connected_clients");

        if (StringUtils.isBlank(maxclients) || StringUtils.isBlank(connectedClients)) {
            // 指标获取失败
            return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }

        double maxClients = Double.parseDouble(maxclients);
        double connected = Double.parseDouble(connectedClients);

        double value = connected / maxClients;

        String result = "maxclinets=" + maxclients + "\nconnected_clients=" + connectedClients;
        if (value > 0.5) {
            return new InspectionTaskMiddlewareDTO(subTaskId,item, metricKey, result, InspectLadderEnum.RISK.getValue());
        }
        return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, result, InspectLadderEnum.NORMAL.getValue());
    }

    public InspectionTaskMiddlewareDTO memoryUsed(Long subTaskId, Map<String, String> infoMap,String item) {
        String metricKey = InspectionMetricKeyEnum.REDIS_ACTUAL_MEMORY_USAGE.getMetricKey();
        String usedMemory = infoMap.get("used_memory");
        String usedMemoryHuman = infoMap.get("used_memory_human");
        String usedMemoryRssHuman = infoMap.get("used_memory_rss_human");
        String usedMemoryPeakHuman = infoMap.get("used_memory_peak_human");
        String totalSystemMemoryHuman = infoMap.get("total_system_memory_human");

        if (StringUtils.isBlank(usedMemory)
                || StringUtils.isBlank(usedMemoryRssHuman)
                || StringUtils.isBlank(usedMemoryPeakHuman)
                || StringUtils.isBlank(totalSystemMemoryHuman)) {
            // 指标获取失败
            return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, "指标获取失败",  InspectLadderEnum.EXCEPT.getValue());
        }

        double value = Double.parseDouble(usedMemory) / 1024 / 1024 / 1024;
        String result = "used_memory_human=" + usedMemoryHuman
                + "\nused_memory_rss_human=" + usedMemoryRssHuman
                + "\nused_memory_peak_human=" + usedMemoryPeakHuman
                + "\ntotal_system_memory_human=" + totalSystemMemoryHuman;
        if (value > 3) {
            return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, result,  InspectLadderEnum.RISK.getValue());
        }
        return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, result,  InspectLadderEnum.NORMAL.getValue());
    }

    public InspectionTaskMiddlewareDTO aof(Long subTaskId, Map<String, String> infoMap,String item) {
        String metricKey = InspectionMetricKeyEnum.REDIS_AOF.getMetricKey();
        String aofCurrentSize = infoMap.get("aof_current_size");
        String aofBaseSize = infoMap.get("aof_base_size");
        String aofLastBgrewriteStatus = infoMap.get("aof_last_bgrewrite_status");
        String aofLastRewriteTimeSec = infoMap.get("aof_last_rewrite_time_sec");

        if (StringUtils.isBlank(aofCurrentSize)
                || StringUtils.isBlank(aofBaseSize)
                || StringUtils.isBlank(aofLastBgrewriteStatus)
                || StringUtils.isBlank(aofLastRewriteTimeSec)) {
            // 指标获取失败
            return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey,  "指标获取失败",  InspectLadderEnum.EXCEPT.getValue());
        }

        double value = Double.parseDouble(aofCurrentSize) / 1024 / 1024 / 1024;
        String result = "aof_current_size=" + aofCurrentSize
                + "\naof_base_size=" + aofBaseSize
                + "\naof_last_bgrewrite_status=" + aofLastBgrewriteStatus
                + "\naof_last_rewrite_time_sec=" + aofLastRewriteTimeSec;
        if (value > 10) {
            return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, result,  InspectLadderEnum.RISK.getValue());
        }
        return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, result,  InspectLadderEnum.NORMAL.getValue());
    }

    public InspectionTaskMiddlewareDTO qps(Long subTaskId, Map<String, String> infoMap,String item) {
        String metricKey = InspectionMetricKeyEnum.REDIS_QPS.getMetricKey();
        String instantaneousOpsPerSec = infoMap.get("instantaneous_ops_per_sec");

        if (StringUtils.isBlank(instantaneousOpsPerSec)) {
            // 指标获取失败
            return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey,  "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }

        long value = Integer.parseInt(instantaneousOpsPerSec);
        String result = "instantaneous_ops_per_sec=" + instantaneousOpsPerSec;
        if (value > 10000) {
            return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, result, InspectLadderEnum.RISK.getValue());
        }
        return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, result, InspectLadderEnum.NORMAL.getValue());
    }

    public InspectionTaskMiddlewareDTO keySpace(Long subTaskId, Map<String, String> infoMap,String item) {
        String metricKey = InspectionMetricKeyEnum.REDIS_KEYSPACE.getMetricKey();
        String result = infoMap.get("keyspace");

        if (StringUtils.isBlank(result)) {
            // 指标获取失败
            return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }

        return new InspectionTaskMiddlewareDTO(subTaskId, item, metricKey, result, InspectLadderEnum.NORMAL.getValue());
    }
}
