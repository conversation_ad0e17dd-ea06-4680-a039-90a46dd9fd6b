package com.xylink.manager.service.redis;

import com.xylink.manager.controller.dto.inspect.RedisConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 统一Redis服务类
 * 
 * 提供基于Redisson的统一Redis操作接口，替代直接使用Jedis的场景
 * 支持单机、哨兵、集群等多种Redis部署模式
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class UnifiedRedisService {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 测试Redis连接
     * 
     * @param host Redis主机
     * @param port Redis端口
     * @param username 用户名
     * @param password 密码
     * @return 连接是否成功
     */
    public boolean testConnection(String host, int port, String username, String password) {
        try {
            // 使用当前配置的RedissonClient测试连接
            // 通过执行一个简单的操作来验证连接
            String testKey = "connection_test_" + System.currentTimeMillis();
            redissonClient.getBucket(testKey).set("test", 1, TimeUnit.SECONDS);
            redissonClient.getBucket(testKey).delete();
            
            log.debug("Redis连接测试成功: {}:{}", host, port);
            return true;
        } catch (Exception e) {
            log.warn("Redis连接测试失败: {}:{}, 错误: {}", host, port, e.getMessage());
            return false;
        }
    }

    /**
     * 获取Redis信息
     *
     * @param section 信息段，如"server", "memory", "replication"等
     * @return Redis信息字符串
     */
    public String getInfo(String section) {
        try {
            // 使用Redisson执行INFO命令
            // 注意：这里使用RedisTemplate作为备选方案，因为Redisson的API可能不同版本有差异
            return redisTemplate.execute(connection -> {
                if (section == null || section.isEmpty()) {
                    return new String(connection.info().getBytes());
                } else {
                    return new String(connection.info(section).getBytes());
                }
            });
        } catch (Exception e) {
            log.error("获取Redis信息失败, section: {}", section, e);
            return "";
        }
    }

    /**
     * 获取Redis完整信息并解析为Map
     * 
     * @param configDTO Redis配置
     * @return 解析后的Redis信息Map
     */
    public Map<String, String> getRedisInfo(RedisConfigDTO configDTO) {
        Map<String, String> infoMap = new HashMap<>();
        
        try {
            // 获取各个section的信息
            String serverInfo = getInfo("server");
            String memoryInfo = getInfo("memory");
            String replicationInfo = getInfo("replication");
            String statsInfo = getInfo("stats");
            String keyspaceInfo = getInfo("keyspace");
            
            // 解析server信息
            parseInfoSection(serverInfo, infoMap);
            // 解析memory信息
            parseInfoSection(memoryInfo, infoMap);
            // 解析replication信息
            parseInfoSection(replicationInfo, infoMap);
            // 解析stats信息
            parseInfoSection(statsInfo, infoMap);
            // 解析keyspace信息
            parseInfoSection(keyspaceInfo, infoMap);
            
            log.debug("成功获取Redis信息，共{}个字段", infoMap.size());
            
        } catch (Exception e) {
            log.error("获取Redis信息失败: {}", configDTO.getHost(), e);
        }
        
        return infoMap;
    }

    /**
     * 解析Redis INFO命令返回的信息段
     * 
     * @param infoSection INFO命令返回的信息段
     * @param resultMap 结果Map
     */
    private void parseInfoSection(String infoSection, Map<String, String> resultMap) {
        if (infoSection == null || infoSection.trim().isEmpty()) {
            return;
        }
        
        String[] lines = infoSection.split("\r\n");
        for (String line : lines) {
            line = line.trim();
            // 跳过注释行和空行
            if (line.startsWith("#") || line.isEmpty()) {
                continue;
            }
            
            // 解析key:value格式
            int colonIndex = line.indexOf(':');
            if (colonIndex > 0) {
                String key = line.substring(0, colonIndex).trim();
                String value = line.substring(colonIndex + 1).trim();
                resultMap.put(key, value);
            }
        }
    }

    /**
     * 检查Redis是否为主节点
     * 
     * @param host Redis主机
     * @param port Redis端口
     * @param username 用户名
     * @param password 密码
     * @return 是否为主节点
     */
    public boolean isRedisMaster(String host, int port, String username, String password) {
        try {
            String replicationInfo = getInfo("replication");
            return replicationInfo.contains("role:master");
        } catch (Exception e) {
            log.warn("检查Redis主节点状态失败: {}:{}", host, port, e);
            return false;
        }
    }

    /**
     * 获取Redis内存使用信息
     * 
     * @return 内存使用信息Map
     */
    public Map<String, String> getMemoryInfo() {
        Map<String, String> memoryInfo = new HashMap<>();
        try {
            String memoryInfoStr = getInfo("memory");
            parseInfoSection(memoryInfoStr, memoryInfo);
        } catch (Exception e) {
            log.error("获取Redis内存信息失败", e);
        }
        return memoryInfo;
    }

    /**
     * 获取Redis统计信息
     * 
     * @return 统计信息Map
     */
    public Map<String, String> getStatsInfo() {
        Map<String, String> statsInfo = new HashMap<>();
        try {
            String statsInfoStr = getInfo("stats");
            parseInfoSection(statsInfoStr, statsInfo);
        } catch (Exception e) {
            log.error("获取Redis统计信息失败", e);
        }
        return statsInfo;
    }

    /**
     * 执行PING命令测试连接
     * 
     * @return PING命令结果
     */
    public String ping() {
        try {
            redissonClient.getRedisNodes().pingAll();
            return "PONG";
        } catch (Exception e) {
            log.error("Redis PING失败", e);
            return "ERROR: " + e.getMessage();
        }
    }

    /**
     * 获取Redis版本信息
     * 
     * @return Redis版本
     */
    public String getRedisVersion() {
        try {
            String serverInfo = getInfo("server");
            Map<String, String> infoMap = new HashMap<>();
            parseInfoSection(serverInfo, infoMap);
            return infoMap.getOrDefault("redis_version", "unknown");
        } catch (Exception e) {
            log.error("获取Redis版本失败", e);
            return "unknown";
        }
    }
}
