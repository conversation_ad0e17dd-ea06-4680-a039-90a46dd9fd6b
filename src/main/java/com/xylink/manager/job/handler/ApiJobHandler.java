package com.xylink.manager.job.handler;

import com.xylink.manager.job.exception.JobExecutionException;
import com.xylink.manager.job.model.*;
import com.xylink.manager.job.util.JobLoggerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * API类型Job处理器
 * 负责执行HTTP API调用任务，每个Job使用独立的HTTP客户端实例
 */
@Slf4j
@Component
public class ApiJobHandler implements JobHandler {

    /**
     * 为每个Job维护独立的RestTemplate实例，确保隔离性
     */
    private final Map<String, RestTemplate> restTemplateCache = new ConcurrentHashMap<>();

    /**
     * 日期时间格式化器
     */
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @PostConstruct
    public void init() {
        log.info("ApiJobHandler初始化完成");
    }

    @Override
    public JobExecutionResult execute(JobContext context) throws JobExecutionException {
        String jobName = context.getJobName();
        String executionId = context.getExecutionId();
        JobConfig jobConfig = context.getJobConfig();
        ApiConfig apiConfig = jobConfig.getApiConfig();

        if (apiConfig == null) {
            throw new JobExecutionException(jobName, executionId, "API配置不能为空");
        }

        log.info("开始执行API Job: jobName={}, executionId={}, url={}, method={}", 
                jobName, executionId, apiConfig.getTargetUrl(), apiConfig.getHttpMethod());

        long startTime = System.currentTimeMillis();
        String startTimeStr = LocalDateTime.now().format(dateTimeFormatter);

        try {
            // 获取或创建独立的RestTemplate实例
            RestTemplate restTemplate = getOrCreateRestTemplate(jobName, apiConfig);

            // 构建HTTP请求
            HttpEntity<String> requestEntity = buildHttpRequest(apiConfig);

            // 记录请求详情（脱敏处理）
            logApiRequest(jobName, executionId, apiConfig, requestEntity);

            // 执行HTTP请求
            ResponseEntity<String> response = executeHttpRequest(restTemplate, apiConfig, requestEntity);

            // 记录响应详情（脱敏处理）
            logApiResponse(jobName, executionId, response);

            // 判断执行结果
            boolean success = isSuccessResponse(response);
            long endTime = System.currentTimeMillis();
            String endTimeStr = LocalDateTime.now().format(dateTimeFormatter);

            if (success) {
                String details = String.format("API调用成功: status=%d, responseLength=%d", 
                        response.getStatusCodeValue(), 
                        response.getBody() != null ? response.getBody().length() : 0);

                log.info("API Job执行成功: jobName={}, executionId={}, status={}, duration={}ms", 
                        jobName, executionId, response.getStatusCodeValue(), endTime - startTime);

                return JobExecutionResult.success(endTime - startTime, details);
            } else {
                String errorMessage = String.format("API调用返回非成功状态码: %d", response.getStatusCodeValue());
                String details = String.format("API调用失败: status=%d, response=%s", 
                        response.getStatusCodeValue(), 
                        JobLoggerUtil.sanitizeData(response.getBody()));

                log.warn("API Job执行失败: jobName={}, executionId={}, status={}, duration={}ms", 
                        jobName, executionId, response.getStatusCodeValue(), endTime - startTime);

                return JobExecutionResult.failure(endTime - startTime, errorMessage, details, 
                        context.getCurrentRetryCount());
            }

        } catch (ResourceAccessException e) {
            // 处理超时异常
            long endTime = System.currentTimeMillis();
            String endTimeStr = LocalDateTime.now().format(dateTimeFormatter);
            String errorMessage = "API调用超时: " + e.getMessage();

            log.error("API Job执行超时: jobName={}, executionId={}, duration={}ms", 
                    jobName, executionId, endTime - startTime, e);

            return JobExecutionResult.failure(endTime - startTime, errorMessage, null, 
                    context.getCurrentRetryCount());

        } catch (Exception e) {
            // 处理其他异常
            long endTime = System.currentTimeMillis();
            String endTimeStr = LocalDateTime.now().format(dateTimeFormatter);
            String errorMessage = "API调用异常: " + e.getMessage();

            log.error("API Job执行异常: jobName={}, executionId={}, duration={}ms", 
                    jobName, executionId, endTime - startTime, e);

            return JobExecutionResult.failure(endTime - startTime, errorMessage, null, 
                    context.getCurrentRetryCount());
        }
    }

    @Override
    public JobType getSupportedType() {
        return JobType.API;
    }

    /**
     * 获取或创建独立的RestTemplate实例
     * 为每个Job创建独立的HTTP客户端，确保配置隔离
     */
    private RestTemplate getOrCreateRestTemplate(String jobName, ApiConfig apiConfig) {
        return restTemplateCache.computeIfAbsent(jobName, key -> {
            SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
            
            // 设置连接超时和读取超时
            factory.setConnectTimeout(Math.min(apiConfig.getRequestTimeoutSeconds() * 1000, 10000)); // 最大10秒连接超时
            factory.setReadTimeout(apiConfig.getRequestTimeoutSeconds() * 1000);

            RestTemplate restTemplate = new RestTemplate(factory);
            
            log.info("为Job创建独立的RestTemplate: jobName={}, connectTimeout={}ms, readTimeout={}ms", 
                    jobName, Math.min(apiConfig.getRequestTimeoutSeconds() * 1000, 10000), apiConfig.getRequestTimeoutSeconds() * 1000);
            
            return restTemplate;
        });
    }

    /**
     * 构建HTTP请求
     */
    private HttpEntity<String> buildHttpRequest(ApiConfig apiConfig) {
        HttpHeaders headers = new HttpHeaders();

        // 设置默认Content-Type
        if (apiConfig.getBody() != null && !apiConfig.getBody().trim().isEmpty()) {
            headers.setContentType(MediaType.APPLICATION_JSON);
        }

        // 添加自定义headers
        if (apiConfig.getHeaders() != null) {
            apiConfig.getHeaders().forEach((key, value) -> {
                try {
                    headers.add(key, value);
                } catch (Exception e) {
                    log.warn("添加HTTP header失败: key={}, value={}", key, JobLoggerUtil.sanitizeData(value), e);
                }
            });
        }

        // 创建请求实体
        String requestBody = apiConfig.getBody();
        return new HttpEntity<>(requestBody, headers);
    }

    /**
     * 执行HTTP请求
     */
    private ResponseEntity<String> executeHttpRequest(RestTemplate restTemplate, ApiConfig apiConfig, 
                                                     HttpEntity<String> requestEntity) {
        HttpMethod httpMethod = HttpMethod.valueOf(apiConfig.getHttpMethod().toUpperCase());
        
        return restTemplate.exchange(
                apiConfig.getTargetUrl(),
                httpMethod,
                requestEntity,
                String.class
        );
    }

    /**
     * 判断响应是否成功（2xx状态码）
     */
    private boolean isSuccessResponse(ResponseEntity<String> response) {
        return response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 记录API请求详情（脱敏处理）
     */
    private void logApiRequest(String jobName, String executionId, ApiConfig apiConfig, HttpEntity<String> requestEntity) {
        try {
            log.info("API请求详情: jobName={}, executionId={}, method={}, url={}, headers={}, bodyLength={}", 
                    jobName, executionId, 
                    apiConfig.getHttpMethod(), 
                    apiConfig.getTargetUrl(),
                    JobLoggerUtil.sanitizeData(requestEntity.getHeaders()),
                    requestEntity.getBody() != null ? requestEntity.getBody().length() : 0);

            // 记录请求体（脱敏）
            if (requestEntity.getBody() != null && !requestEntity.getBody().trim().isEmpty()) {
                log.debug("API请求体: jobName={}, executionId={}, body={}", 
                        jobName, executionId, JobLoggerUtil.sanitizeData(requestEntity.getBody()));
            }
        } catch (Exception e) {
            log.warn("记录API请求详情失败: jobName={}, executionId={}", jobName, executionId, e);
        }
    }

    /**
     * 记录API响应详情（脱敏处理）
     */
    private void logApiResponse(String jobName, String executionId, ResponseEntity<String> response) {
        try {
            log.info("API响应详情: jobName={}, executionId={}, status={}, headers={}, bodyLength={}", 
                    jobName, executionId, 
                    response.getStatusCodeValue(),
                    JobLoggerUtil.sanitizeData(response.getHeaders()),
                    response.getBody() != null ? response.getBody().length() : 0);

            // 记录响应体（脱敏，仅记录前500字符）
            if (response.getBody() != null && !response.getBody().trim().isEmpty()) {
                String responseBody = response.getBody();
                String truncatedBody = responseBody.length() > 500 ? responseBody.substring(0, 500) + "..." : responseBody;
                log.debug("API响应体: jobName={}, executionId={}, body={}", 
                        jobName, executionId, JobLoggerUtil.sanitizeData(truncatedBody));
            }
        } catch (Exception e) {
            log.warn("记录API响应详情失败: jobName={}, executionId={}", jobName, executionId, e);
        }
    }

    /**
     * 清理指定Job的RestTemplate缓存（用于配置更新时）
     */
    public void clearRestTemplateCache(String jobName) {
        RestTemplate removed = restTemplateCache.remove(jobName);
        if (removed != null) {
            log.info("清理Job的RestTemplate缓存: jobName={}", jobName);
        }
    }

    /**
     * 清理所有RestTemplate缓存
     */
    public void clearAllRestTemplateCache() {
        int size = restTemplateCache.size();
        restTemplateCache.clear();
        log.info("清理所有RestTemplate缓存，共清理{}个实例", size);
    }

    /**
     * 获取缓存的RestTemplate数量
     */
    public int getCachedRestTemplateCount() {
        return restTemplateCache.size();
    }
}