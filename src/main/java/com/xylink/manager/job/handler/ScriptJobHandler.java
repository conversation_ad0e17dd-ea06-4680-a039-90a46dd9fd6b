package com.xylink.manager.job.handler;

import com.xylink.manager.job.exception.JobExecutionException;
import com.xylink.manager.job.model.*;
import com.xylink.manager.job.security.ScriptSecurityManager;
import com.xylink.manager.job.util.JobLoggerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 脚本类型Job处理器
 * 负责执行脚本任务，每个脚本Job使用独立的进程执行
 */
@Slf4j
@Component
public class ScriptJobHandler implements JobHandler {

    @Autowired
    private ScriptSecurityManager scriptSecurityManager;

    /**
     * 日期时间格式化器
     */
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 默认脚本超时时间（秒）
     */
    private static final int DEFAULT_TIMEOUT_SECONDS = 300;

    /**
     * 输出缓冲区最大长度
     */
    private static final int MAX_OUTPUT_LENGTH = 10000;

    @PostConstruct
    public void init() {
        log.info("ScriptJobHandler初始化完成");
    }

    @Override
    public JobExecutionResult execute(JobContext context) throws JobExecutionException {
        String jobName = context.getJobName();
        String executionId = context.getExecutionId();
        JobConfig jobConfig = context.getJobConfig();
        ScriptConfig scriptConfig = jobConfig.getScriptConfig();

        if (scriptConfig == null) {
            throw new JobExecutionException(jobName, executionId, "脚本配置不能为空");
        }

        log.info("开始执行脚本Job: jobName={}, executionId={}, script={}", 
                jobName, executionId, scriptConfig.getPath());

        long startTime = System.currentTimeMillis();
        String startTimeStr = LocalDateTime.now().format(dateTimeFormatter);

        Process process = null;
        try {
            // 使用安全管理器验证脚本路径
            scriptSecurityManager.validateScriptPath(jobName, executionId, scriptConfig.getPath());

            // 验证脚本参数安全性
            scriptSecurityManager.validateScriptArguments(jobName, executionId, scriptConfig.getArguments());

            // 验证工作目录安全性
            scriptSecurityManager.validateWorkingDirectory(jobName, executionId, scriptConfig.getWorkingDirectory());

            // 验证环境变量安全性
            scriptSecurityManager.validateEnvironmentVariables(jobName, executionId, scriptConfig.getEnvironmentVariables());

            // 构建进程命令
            List<String> command = buildSecureCommand(scriptConfig);

            // 记录脚本执行详情
            logScriptExecution(jobName, executionId, scriptConfig, command);

            // 创建独立进程执行脚本
            ProcessBuilder processBuilder = createProcessBuilder(command, scriptConfig);
            process = processBuilder.start();

            // 执行脚本并等待结果
            ScriptExecutionResult scriptResult = executeScriptProcess(process, scriptConfig, jobName, executionId);

            // 处理执行结果
            long endTime = System.currentTimeMillis();
            String endTimeStr = LocalDateTime.now().format(dateTimeFormatter);

            if (scriptResult.isSuccess()) {
                String details = String.format("脚本执行成功: exitCode=%d, stdoutLength=%d, stderrLength=%d", 
                        scriptResult.getExitCode(), 
                        scriptResult.getStdout().length(), 
                        scriptResult.getStderr().length());

                log.info("脚本Job执行成功: jobName={}, executionId={}, exitCode={}, duration={}ms", 
                        jobName, executionId, scriptResult.getExitCode(), endTime - startTime);

                JobExecutionResult result = JobExecutionResult.success(endTime - startTime, details);
                result.setStartTime(startTimeStr);
                result.setEndTime(endTimeStr);
                result.setRetryCount(context.getCurrentRetryCount());
                return result;
            } else {
                String errorMessage = String.format("脚本执行失败: exitCode=%d", scriptResult.getExitCode());
                String details = String.format("脚本执行失败: exitCode=%d, stdout=%s, stderr=%s", 
                        scriptResult.getExitCode(), 
                        JobLoggerUtil.truncateString(scriptResult.getStdout(), 500),
                        JobLoggerUtil.truncateString(scriptResult.getStderr(), 500));

                log.warn("脚本Job执行失败: jobName={}, executionId={}, exitCode={}, duration={}ms", 
                        jobName, executionId, scriptResult.getExitCode(), endTime - startTime);

                JobExecutionResult result = JobExecutionResult.failure(endTime - startTime, errorMessage, details, context.getCurrentRetryCount());
                result.setStartTime(startTimeStr);
                result.setEndTime(endTimeStr);
                return result;
            }

        } catch (InterruptedException e) {
            // 处理中断异常
            long endTime = System.currentTimeMillis();
            String endTimeStr = LocalDateTime.now().format(dateTimeFormatter);
            String errorMessage = "脚本执行被中断: " + e.getMessage();

            log.warn("脚本Job执行被中断: jobName={}, executionId={}, duration={}ms", 
                    jobName, executionId, endTime - startTime, e);

            // 确保进程被终止
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }

            Thread.currentThread().interrupt();
            JobExecutionResult result = JobExecutionResult.failure(endTime - startTime, errorMessage, null, context.getCurrentRetryCount());
            result.setStartTime(startTimeStr);
            result.setEndTime(endTimeStr);
            return result;

        } catch (Exception e) {
            // 处理其他异常
            long endTime = System.currentTimeMillis();
            String endTimeStr = LocalDateTime.now().format(dateTimeFormatter);
            String errorMessage = "脚本执行异常: " + e.getMessage();

            log.error("脚本Job执行异常: jobName={}, executionId={}, duration={}ms", 
                    jobName, executionId, endTime - startTime, e);

            // 确保进程被终止
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }

            JobExecutionResult result = JobExecutionResult.failure(endTime - startTime, errorMessage, null, context.getCurrentRetryCount());
            result.setStartTime(startTimeStr);
            result.setEndTime(endTimeStr);
            return result;
        }
    }

    @Override
    public JobType getSupportedType() {
        return JobType.SCRIPT;
    }



    /**
     * 构建安全的执行命令
     */
    private List<String> buildSecureCommand(ScriptConfig scriptConfig) {
        List<String> command = new ArrayList<>();
        command.add(scriptConfig.getPath());

        // 添加已验证的安全参数
        if (scriptConfig.getArguments() != null && !scriptConfig.getArguments().isEmpty()) {
            for (String arg : scriptConfig.getArguments()) {
                if (arg != null) {
                    command.add(arg);
                }
            }
        }

        return command;
    }

    /**
     * 创建ProcessBuilder
     */
    private ProcessBuilder createProcessBuilder(List<String> command, ScriptConfig scriptConfig) {
        ProcessBuilder processBuilder = new ProcessBuilder(command);

        // 设置工作目录
        if (scriptConfig.getWorkingDirectory() != null && !scriptConfig.getWorkingDirectory().trim().isEmpty()) {
            File workingDir = new File(scriptConfig.getWorkingDirectory());
            if (workingDir.exists() && workingDir.isDirectory()) {
                processBuilder.directory(workingDir);
                log.debug("设置脚本工作目录: {}", workingDir.getAbsolutePath());
            } else {
                log.warn("指定的工作目录不存在或不是目录，使用默认工作目录: {}", scriptConfig.getWorkingDirectory());
            }
        }

        // 设置环境变量
        if (scriptConfig.getEnvironmentVariables() != null && !scriptConfig.getEnvironmentVariables().isEmpty()) {
            processBuilder.environment().putAll(scriptConfig.getEnvironmentVariables());
            log.debug("设置脚本环境变量: {}", JobLoggerUtil.sanitizeData(scriptConfig.getEnvironmentVariables()));
        }

        // 重定向错误流到标准输出流，便于统一处理
        processBuilder.redirectErrorStream(false);

        return processBuilder;
    }

    /**
     * 记录脚本执行详情（脱敏处理）
     */
    private void logScriptExecution(String jobName, String executionId, ScriptConfig scriptConfig, List<String> command) {
        try {
            log.info("脚本执行详情: jobName={}, executionId={}, script={}, arguments={}, workingDir={}", 
                    jobName, executionId, 
                    scriptConfig.getPath(),
                    JobLoggerUtil.sanitizeData(scriptConfig.getArguments()),
                    scriptConfig.getWorkingDirectory());

            // 记录环境变量（脱敏）
            if (scriptConfig.getEnvironmentVariables() != null && !scriptConfig.getEnvironmentVariables().isEmpty()) {
                log.debug("脚本环境变量: jobName={}, executionId={}, env={}", 
                        jobName, executionId, JobLoggerUtil.sanitizeData(scriptConfig.getEnvironmentVariables()));
            }

            // 记录完整命令（脱敏）
            log.debug("脚本执行命令: jobName={}, executionId={}, command={}", 
                    jobName, executionId, JobLoggerUtil.sanitizeData(command));
        } catch (Exception e) {
            log.warn("记录脚本执行详情失败: jobName={}, executionId={}", jobName, executionId, e);
        }
    }

    /**
     * 执行脚本进程并等待结果
     */
    private ScriptExecutionResult executeScriptProcess(Process process, ScriptConfig scriptConfig, 
                                                      String jobName, String executionId) throws InterruptedException, IOException {
        
        int timeoutSeconds = scriptConfig.getTimeoutSeconds() > 0 ? 
                scriptConfig.getTimeoutSeconds() : DEFAULT_TIMEOUT_SECONDS;

        // 启动输出读取线程
        StringBuilder stdout = new StringBuilder();
        StringBuilder stderr = new StringBuilder();
        
        Thread stdoutReader = new Thread(() -> readProcessOutput(process.getInputStream(), stdout, "stdout", jobName, executionId));
        Thread stderrReader = new Thread(() -> readProcessOutput(process.getErrorStream(), stderr, "stderr", jobName, executionId));
        
        stdoutReader.start();
        stderrReader.start();

        // 等待进程完成或超时
        boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);
        
        if (!finished) {
            // 超时，强制终止进程
            log.warn("脚本执行超时，强制终止进程: jobName={}, executionId={}, timeout={}s", 
                    jobName, executionId, timeoutSeconds);
            
            process.destroyForcibly();
            
            // 等待进程真正终止
            process.waitFor(5, TimeUnit.SECONDS);
            
            return new ScriptExecutionResult(false, -1, "脚本执行超时", "脚本执行超时被强制终止");
        }

        // 等待输出读取线程完成
        stdoutReader.join(5000);
        stderrReader.join(5000);

        int exitCode = process.exitValue();
        boolean success = (exitCode == 0);

        String stdoutStr = stdout.toString();
        String stderrStr = stderr.toString();

        // 记录脚本输出（脱敏和截断）
        logScriptOutput(jobName, executionId, exitCode, stdoutStr, stderrStr);

        return new ScriptExecutionResult(success, exitCode, stdoutStr, stderrStr);
    }

    /**
     * 读取进程输出
     */
    private void readProcessOutput(java.io.InputStream inputStream, StringBuilder output, 
                                  String streamType, String jobName, String executionId) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 防止输出过长导致内存问题
                if (output.length() < MAX_OUTPUT_LENGTH) {
                    output.append(line).append("\n");
                } else if (output.length() == MAX_OUTPUT_LENGTH) {
                    output.append("...[输出被截断，超过最大长度限制]");
                    log.warn("脚本{}输出过长被截断: jobName={}, executionId={}, maxLength={}", 
                            streamType, jobName, executionId, MAX_OUTPUT_LENGTH);
                }
            }
        } catch (IOException e) {
            log.error("读取脚本{}输出异常: jobName={}, executionId={}", streamType, jobName, executionId, e);
        }
    }

    /**
     * 记录脚本输出（脱敏处理）
     */
    private void logScriptOutput(String jobName, String executionId, int exitCode, String stdout, String stderr) {
        try {
            log.info("脚本执行结果: jobName={}, executionId={}, exitCode={}, stdoutLength={}, stderrLength={}", 
                    jobName, executionId, exitCode, stdout.length(), stderr.length());

            // 记录标准输出（脱敏和截断）
            if (stdout != null && !stdout.trim().isEmpty()) {
                String truncatedStdout = JobLoggerUtil.truncateString(stdout, 1000);
                log.debug("脚本标准输出: jobName={}, executionId={}, stdout={}", 
                        jobName, executionId, JobLoggerUtil.sanitizeData(truncatedStdout));
            }

            // 记录错误输出（脱敏和截断）
            if (stderr != null && !stderr.trim().isEmpty()) {
                String truncatedStderr = JobLoggerUtil.truncateString(stderr, 1000);
                log.debug("脚本错误输出: jobName={}, executionId={}, stderr={}", 
                        jobName, executionId, JobLoggerUtil.sanitizeData(truncatedStderr));
            }
        } catch (Exception e) {
            log.warn("记录脚本输出失败: jobName={}, executionId={}", jobName, executionId, e);
        }
    }

    /**
     * 脚本执行结果内部类
     */
    private static class ScriptExecutionResult {
        private final boolean success;
        private final int exitCode;
        private final String stdout;
        private final String stderr;

        public ScriptExecutionResult(boolean success, int exitCode, String stdout, String stderr) {
            this.success = success;
            this.exitCode = exitCode;
            this.stdout = stdout != null ? stdout : "";
            this.stderr = stderr != null ? stderr : "";
        }

        public boolean isSuccess() {
            return success;
        }

        public int getExitCode() {
            return exitCode;
        }

        public String getStdout() {
            return stdout;
        }

        public String getStderr() {
            return stderr;
        }
    }
}