package com.xylink.manager.model.strategy;

import com.google.common.collect.ImmutableMap;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.RedisConstants;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.IDeployService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.xylink.manager.service.redis.UnifiedRedisService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@Slf4j
@Component
public class RedisClusterStrategy extends CMRefreshStrategy {

    private final String appLabel = "private-redis";

    private final int port = 6379;

    @Autowired
    private IDeployService deployService;

    @Autowired
    private CMRefreshQueueService cmRefreshQueueService;

    @Autowired
    private UnifiedRedisService unifiedRedisService;

    @Override
    public String getAppLabel() {
        return appLabel;
    }

    @Override
    public boolean refreshIps() {
        log.info("{} refreshIps invoke .", this.getClass().getSimpleName());
        try {
            List<Pod> pods = deployService.listPodsByAppLabel(appLabel);
            if (CollectionUtils.isEmpty(pods)) {
                return true;
            }
            ConfigMap allIp = deployService.getConfigMapAllIp();
            Map<String, String> allIpMap = allIp.getData();
            String mainRedisIp = allIpMap.get(NetworkConstants.MAIN_REDIS_IP);

            ConfigMap allRedis = deployService.getConfigMapByName(Constants.CONFIGMAP_REDIS, Constants.NAMESPACE_DEFAULT);
            //all-redis 未配置的情况下，只需更新all-ip
            if (null == allRedis) {
                if (isHostConnectable(mainRedisIp, port)) {
                    return true;
                }
                Optional<Pod> optionalPod = pods.stream()
                        .filter(pod -> isHostConnectable(pod.getHostIp(), port) && isRedisMaster(pod.getHostIp()))
                        .findFirst();
                if (optionalPod.isPresent()) {
                    deployService.patchConfigMapAllIpForAddData(ImmutableMap.of(NetworkConstants.MAIN_REDIS_IP, optionalPod.get().getHostIp()));
                    return true;
                } else {
                    if (!Thread.currentThread().getName().matches("CM_Refresh_Pool-\\d") && !cmRefreshQueueService.cmRefreshQueue.contains(this)) {
                        log.info("Add to Retry-QUEUE. {}", this);
                        cmRefreshQueueService.cmRefreshQueue.add(this);
                    }
                    return false;
                }
            } else {
                Map<String, String> allRedisMap = allRedis.getData();
                String redisMasterIp = allRedisMap.get(NetworkConstants.REDIS_MASTER_IP);
                if (isHostConnectable(redisMasterIp, port) && isRedisMaster(redisMasterIp)) {   //当前 redis_master 可用 并且 还是master，则无需更新all-redis
                    if (!mainRedisIp.equals(redisMasterIp)) {
                        deployService.patchConfigMapAllIpForAddData(ImmutableMap.of(NetworkConstants.MAIN_REDIS_IP, redisMasterIp));
                    }
                    return true;
                }
                Optional<Pod> optionalPod = pods.stream()
                        .filter(pod -> isHostConnectable(pod.getHostIp(), port) && isRedisMaster(pod.getHostIp()))
                        .findFirst();
                if (optionalPod.isPresent()) {
                    Map<String, String> data = new HashMap<>();

                    Optional<Map.Entry<String, String>> mapEntry = allRedisMap.entrySet().stream().filter(entry -> RedisConstants.REDIS_ROLE_MASTER.equals(entry.getValue())).findFirst();
                    mapEntry.ifPresent(stringStringEntry -> data.put(stringStringEntry.getKey(), RedisConstants.REDIS_ROLE_SLAVE));

                    data.put(NetworkConstants.REDIS_MASTER_IP, optionalPod.get().getHostIp());
                    data.put(optionalPod.get().getNodeName() + RedisConstants.REDIS_ROLE_SUFFIX, RedisConstants.REDIS_ROLE_MASTER);

                    //更新 all-redis : REDIS_MASTER_IP
                    deployService.patchConfigMap(Constants.CONFIGMAP_REDIS, Constants.NAMESPACE_DEFAULT, d -> {
                        d.putAll(data);
                    });
                    //更新 all-ip : MAIN_REDIS_IP
                    deployService.patchConfigMapAllIpForAddData(ImmutableMap.of(NetworkConstants.MAIN_REDIS_IP, optionalPod.get().getHostIp()));
                    return true;
                } else {
                    if (!Thread.currentThread().getName().matches("CM_Refresh_Pool-\\d") && !cmRefreshQueueService.cmRefreshQueue.contains(this)) {
                        log.info("Add to Retry-QUEUE. {}", this);
                        cmRefreshQueueService.cmRefreshQueue.add(this);
                    }
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("{} refreshIps error .", this.getClass().getSimpleName(), e);
            return false;
        }
    }

    private boolean isRedisMaster(String hostIp) {
        try {
            // 使用统一Redis服务检查是否为主节点
            boolean isMaster = unifiedRedisService.isRedisMaster(hostIp, 6379, "disrrew", "rwQeDQhWBMUU1&W%rL#CUgh8*M#MBG");
            if (isMaster) {
                log.info("Redis[{}] is master. ", hostIp);
            }
            return isMaster;
        } catch (Exception e) {
            log.warn("Check redis master error. hostIp = {}", hostIp);
            return false;
        }
    }
}
