#spring.main.web_environment=true
server.servlet.context-path=/manager
server.port=18028
base.dir=${manager.base.dir:/mnt/xylink}
server.compression.enabled=true
# application.properties
server.tomcat.max-swallow-size=10MB

spring.banner.location=banner.txt
spring.main.banner-mode=LOG
spring.main.allow-circular-references=true

# Redis配置 - 统一使用Redisson客户端
# Redisson提供更好的性能和分布式功能支持
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms
spring.redis.database=0
# Redis连接池配置（优化性能）
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=2
spring.redis.jedis.pool.max-wait=2000ms
# Redis密码配置（如果需要）
# spring.redis.password=your-password
# Redis SSL配置（如果需要）
# spring.redis.ssl=false

#logging.level.root=trace
logging.level.com.xylink=info
spring.application.name=manager
server.error.whitelabel.enabled=false
server.tomcat.connection-timeout=5000ms

spring.security.ignoringUrlList[0]=/error/**
spring.security.ignoringUrlList[1]=/static/**
spring.security.ignoringUrlList[2]=/api/rest/**
spring.security.ignoringUrlList[3]=/asset-manifest.json
spring.security.ignoringUrlList[4]=/favicon.ico
spring.security.ignoringUrlList[5]=/manifest.json
spring.security.ignoringUrlList[6]=/precache-manifest**
spring.security.ignoringUrlList[7]=/service-worker.js
spring.security.ignoringUrlList[8]=/server.svg
spring.security.ignoringUrlList[9]=/index.html
spring.security.ignoringUrlList[10]=/
spring.security.ignoringUrlList[11]=/image/**
spring.security.ignoringUrlList[12]=/node/notify
spring.security.ignoringUrlList[13]=/node/types
spring.security.ignoringUrlList[14]=/open/manager
spring.security.ignoringUrlList[15]=/version/info
spring.security.ignoringUrlList[16]=/support/hostname/check
spring.security.ignoringUrlList[17]=/proper/list/*
spring.security.ignoringUrlList[18]=/alert/api/*
spring.security.ignoringUrlList[19]=/query/api/internal/node/info
spring.security.ignoringUrlList[20]=/query/api/internal/node/service/info
spring.security.ignoringUrlList[21]=/node/types/anke
spring.security.ignoringUrlList[22]=/mysql/slave/error/report
spring.security.ignoringUrlList[23]=/server/config/mysql/*/readonly/*
spring.security.ignoringUrlList[24]=/#/errPage
spring.security.ignoringUrlList[25]=/backUp/notify
spring.security.ignoringUrlList[26]=/mysql/switch/mode
spring.security.ignoringUrlList[27]=/nightingale/**
spring.security.ignoringUrlList[28]=/noah/**
spring.security.ignoringUrlList[29]=/tiangong/**
spring.security.ignoringUrlList[30]=/OauthIndex.html
spring.security.ignoringUrlList[31]=/ThirdCallback.html
spring.security.ignoringUrlList[32]=/applicationSecurity/**
spring.security.ignoringUrlList[33]=/micro/**
spring.security.ignoringUrlList[34]=/autoupgrade/api/**
spring.security.ignoringUrlList[35]=/internal/client/terminal/info
spring.security.ignoringUrlList[36]=/peer/scouts/node/**
spring.security.ignoringUrlList[37]=/internal/upload/**
spring.security.ignoringUrlList[38]=/peer/server/config/**
spring.security.ignoringUrlList[39]=/server/config/haproxy/*
spring.security.ignoringUrlList[40]=/openapi/**

# multipart
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=1MB
spring.servlet.multipart.location=/tmp
spring.servlet.multipart.max-file-size=-1
spring.servlet.multipart.max-request-size=-1

upgrade.storage.dir=${base.dir}/upgrade
upgrade.storage.log-folder=${base.dir}/upgrade/logs
upgrade.storage.log-compare=${base.dir}/upgrade/compare
upgrade.storage.file-folder=files
upgrade.storage.tmp-folder=tmp
upgrade.storage.upgrade-shell=upgrade_shell.sh
upgrade.storage.security-validation=true
shell.script.dir=/usr/libra/script

#db management
db.backupcommand=mysqldump

db.databasenames=ainemo,buffet,charge,contact,dating,dating_job,external,health,inspection,monitor,task,vcs,vote
db.statis.databasenames=datareal,statis
db.surv.databasenames=surveillance
db.edu.databasenames=education,filemanage,message_push
db.webrtc.databasenames=uaa
db.matrix.databasenames=matrix

db.backupdir=/usr/libra/mysqlbackup/
db.restorecommand=mysql
#These settings should be compatabile with /etc/craft/dbupgrade.properties
db.upgradecommand=python
db.upgradecommand.arg=/usr/sqlupgrade/dbupgrade/main.py
db.upgradecommand.workdir=/usr/sqlupgrade/dbupgrade/
db.upgradecommand.upgradesql.dir=/usr/sqlupgrade/dbupgrade/upgrade_sql/

db.host.backupdir=${base.dir}/db/mysqlbackup/
db.host.statis.backupdir=${base.dir}/db/statismysqlbackup/
db.host.surv.backupdir=${base.dir}/db/survmysqlbackup/
db.host.hbase.backupdir=${base.dir}/db/hbasebackup/
db.host.edu.backupdir=${base.dir}/db/edubackup/
db.host.webrtc.backupdir=${base.dir}/db/wertcbackup/
db.host.matrix.backupdir=${base.dir}/db/matrixbackup/
db.host.nightingale.backupdir=${base.dir}/db/nightingalebackup/

db.upgrade.image=hub.xylink.com:5000/private_cloud/mysql_upgrade:2.0
db.backup.restore.image=hub.xylink.com:5000/private_cloud/mysql

db.upgradecommand.host.prop.path=${base.dir}/db/dbupgrade.properties
db.upgradecommand.prop.path=/etc/craft/dbupgrade.properties
db.upgradecommand.host.workdir=${base.dir}/db/sqlupgrade/dbupgrade/
db.upgradecommand.host.upgradesql.dir=${base.dir}/db/sqlupgrade/dbupgrade/upgrade_sql/

server.config.beyond-control[0]=activemq
server.config.beyond-control[1]=nginx
server.config.beyond-control[2]=privatemanager
server.config.beyond-control[3]=redis
client.log.root.path=${base.dir}/cfs/logserver/client/
hvv.log.root.path=${base.dir}/hvv/

server.script.dir=/Users/<USER>/work/code/manager/src/main/resources/scripts/
#mail config
#private image servlet in pivotor uses this local dir too
privatecloud.filestorage.localdir=/tmp
privatecloud.clientstorage.tmpdir=/tmp
privatecloud.script.dir=${base.dir}/private_manager/script
stream.replace.ignoreList[0]=/client/upload

#user config
server.user.config.dir=/usr/
#accesslog
server.tomcat.accesslog.buffered=false
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.prefix=localhost_access_log
server.tomcat.accesslog.suffix=.log
server.tomcat.basedir=${logging.path}
#server.tomcat.accesslog.pattern=%h %I %t "%r" %s %b %D "%{Referer}i" "%{User-Agent}i "%{X-Forwarded-For}i"
server.tomcat.accesslog.pattern=%h %l %u %t "%r" %s %b %D %I "%{Referer}i" "%{User-Agent}i" "%{X-Forwarded-For}i"
server.tomcat.accesslog.request-attributes-enabled=true

#k8s config
k8s.config.path=${base.dir}/kubeconfig
k8s.protocol.default.http=true
k8s.prefix.url=http://127.0.0.1:8080
harbor.prefix.url=http://hub.xylink.com:5000

# influxdb info:
influxdb.url=http://127.0.0.1:8086

# interval unit minutes
mail.alert.interval=240
license.alert.interval=1440
main.log.dir=${base.dir}/logs/

# unit ms
alert.check.initial.delay=1200000
alert.check.fixed.rate=600000
license.check.initial.delay=6000000
license.check.fixed.rate=600000
license.alert.begin.days=30

package.root.path=/home/<USER>


#captcha
captcha.enableCaptcha=true
captcha.urlList[0]=/login

captcha.conf.kaptcha.border=no
captcha.conf.kaptcha.textproducer.font.color=73,190,56
captcha.conf.kaptcha.textproducer.char.string=123456789ABCDEF
#captcha.conf.kaptcha.image.width=128
#captcha.conf.kaptcha.image.height=40
#captcha.conf.kaptcha.textproducer.font.size=32
captcha.conf.kaptcha.textproducer.char.length=5
captcha.conf.kaptcha.background.clear.from=white
captcha.conf.kaptcha.background.clear.to=white
captcha.conf.kaptcha.noise.color=245,245,245


#user
user.list.admin=111111
user.list.guest=111111
user.list.develop=develop


#role admin
user.role.admin[0].serverManager=ServiceList,ServiceStatus,MirrorList,StorageConfig,ServiceMemory,dns,iptables,RecoveryConfig,SiteList,HostMapping
#\u6280\u672F\u7EC4\u4EF6
user.role.admin[1].serverMonitor=DatabaseList,Middleware,Oceanbase
user.role.admin[2].clientManager=ClientUpdate,RoomsFactory,GrayLevel,GrayList,ErrorCode,PlatformGreyList,SDKLibraryUpload,CustomKeyManage,ClientDownloadConfig,DeviceCenterTask
user.role.admin[3].baseService=ProductRegist,SmsConfig,MailServerConfig,DB,LogDownload,TrustedDevice,FusionServerConfig,CertificateConfig,DomainManager,RemoteSupport,PrivacyConfig,AutoCloudPartitionConfig,DmcuManage,MeetingMonitor,HaproxyConfigMonitor,AreaCode,ClusterConfig,MultiNetDomainConversion,CommonProblem,MultiRegionConfig,ActiveStandbySetting
user.role.admin[4].configCenter=ConfigRepo,ConfigVarManage,HistoryVersion,DeployManage,NginxManage
user.role.admin[5].auditLog=EnterpriseAudit,CloudMeetRoomAudit,TerminalAudit,PlatformLog
#\u76D1\u63A7\u5DE1\u68C0
user.role.admin[6].monitorView=ServerWatchList,MonitorConfig,MonitorDashboard,ServerInspect,AdvancedQuery,ServiceTracing
#\u544A\u8B66\u7BA1\u7406
user.role.admin[7].warningManage=WarnConfig,WarnDetail,CurrentWarning,HistoryWarning,WarningRule,WarningConfig,ShieldRule
user.role.admin[8].upgradeDeploy=ImageManage
user.role.admin[9].serverManage=serverManageList,serverManageInstanceList
user.role.admin[10].systemManage=networkManage,dateTimeManage


#role guest
user.role.guest[0].baseService=LogDownload   

#role develop
user.role.develop[0].serverManager=ServiceList,ServiceStatus,ServiceUpdate,MirrorList,StorageConfig,ServiceMemory,dns,iptables,RecoveryConfig,SiteList,HostMapping
#\u6280\u672F\u7EC4\u4EF6
user.role.develop[1].serverMonitor=DatabaseList,Middleware,Oceanbase
user.role.develop[2].clientManager=ClientUpdate,RoomsFactory,GrayLevel,GrayList,ErrorCode,PlatformGreyList,SDKLibraryUpload,CustomKeyManage,ClientDownloadConfig,DeviceCenterTask
user.role.develop[3].baseService=ProductRegist,SmsConfig,MailServerConfig,DB,LogDownload,TrustedDevice,FusionServerConfig,CertificateConfig,DomainManager,RemoteSupport,PrivacyConfig,AutoCloudPartitionConfig,DmcuManage,MeetingMonitor,HaproxyConfigMonitor,AreaCode,ClusterConfig,MultiNetDomainConversion,CommonProblem,MultiRegionConfig,ActiveStandbySetting
user.role.develop[4].develop=Debug,Tools,Devices,Feature,MeetingConfig,MmsConfig,EduConfig,ClientConfig,Deploy,DBBackup,WhiteList,AudioConfig,Hierarchy,APIManage,VersionUpgrade,GpuBlackList,DataMigration,NetworkDetection,HvvLogDownload,WebConfig,APIInterfaceManage,CryptoConfig,MonitorDetail,BridgeConfig,TerminalModelManagement,APIInterfaceConfig
user.role.develop[5].configCenter=ConfigRepo,ConfigVarManage,HistoryVersion,DeployManage,NginxManage
user.role.develop[6].auditLog=EnterpriseAudit,CloudMeetRoomAudit,TerminalAudit,PlatformLog
#\u76D1\u63A7\u5DE1\u68C0
user.role.develop[7].monitorView=ServerWatchList,MonitorConfig,MonitorDashboard,ServerInspect,AdvancedQuery,ServiceTracing
#\u544A\u8B66\u7BA1\u7406
user.role.develop[8].warningManage=WarnConfig,WarnDetail,CurrentWarning,HistoryWarning,WarningRule,WarningConfig,ShieldRule
user.role.develop[9].upgradeDeploy=ImageManage
user.role.develop[10].serverManage=serverManageList,serverManageInstanceList
user.role.develop[11].systemManage=networkManage,dateTimeManage
#role devops
user.role.devops[0].configCenter=ConfigRepo,ConfigVarManage,HistoryVersion,DeployManage,NginxManage
user.role.devops[1].upgradeDeploy=ImageManage
user.role.devops[2].serverManager=ServiceUpdate,ServiceList
user.role.devops[3].serverManage=serverManageList,serverManageInstanceList,StorageConfig

#role city_admin
user.role.city_admin[0].serverManager=ServiceList
user.role.city_admin[1].serverMonitor=ServerWatchList,WarnDetail
user.role.city_admin[2].baseService=LogDownload

frp.file=/etc/privatefrpc/privatefrpc.ini

main.services=private-mms-logic,private-mms-edge,private-mms-dispatcher,private-mms-permission,private-access,private-iauth,private-charge,private-uss,private-pivotor

main.oss.path=${base.dir}/openresty/nginx_main/oss/logo


nemo_l.modal=NE60,NE20,NE80,AE2060,AE2080,AE2020,AE2022,AE2062,AE2082
nemo_m.modal=ME20,AE20,AE22,ME40,AE40,AE42,ME50S,AE600,ME40ii,ME60,AE700,ES700,ME55S,ME60S,AE650,ME20-4G,AE350,AE380
nemo_b.modal=ME90,AE70,AE90,AE95,ME95

#deploy-platform=publicCloud

db.password.encrypt.key=A9B9C8DEF2021
db.password.encrypt.iv=9982021

#??main????????????
main.db.services=private-access,private-basicinfo,private-basic-management,private-captcha,private-bill,private-buffet,private-business-download-service,private-charge,private-callpermission,private-clientconfig,private-cloudmeetingroom,private-contact,private-contact-schedule,private-css,private-dating,private-iauth,private-im,private-inspection,private-live,private-liveness-probe,private-locator,private-logserver,private-ocean,private-page,private-pivotor,private-push,private-roster-msg,private-sdkcallback,private-externalweb,private-sensitiveword,private-sitecode,private-surv-charge,private-thirdadapter,private-tsa,private-userpermission,private-uss,private-vcs,private-vodbroker,private-vodmanager,private-vote,private-vote-statistics,private-azkaban,private-yarn,private-meeting-recorder,private-statis-datacenter,private-statis-quality,private-statis-meeting,private-statis-monitor,private-statis-live,private-canal,private-edu-access,private-basic-xyauth,private-aistorage,private-message-push,private-tsa-mp,private-devicestate,private-data-transfer-server

#??statis??????????
statis.db.services=private-azkaban,private-yarn,private-hive,private-datafact,private-meeting-recorder,private-dcs,private-des,private-statis-datacenter,private-statis-quality,private-statis-meeting,private-statis-monitor,private-statis-live,private-dcm,private-service-inspection,private-noah,private-nightingale,private-nightingale-categraf-new,private-data-transfer-server

#??surv??????????
surv.db.services=private-survbiz

#??matrix??????????
matrix.db.services=private-matrix-app,private-matrix-alg

#??uaa????????????
uaa.db.services=private-uaa-admin,private-uaa-api,private-uaa-base

#??edu????????????
edu.db.services=private-edu-manage,private-edu-adapter,private-edu-dating,private-edu-preview,private-edu-resource,private-education,private-examination,private-file-manage

# console token check url
console.token.check.url=/api/rest/internal/v1/en/buffet/indexConsle/tokenChecking

dts.workspace=${base.dir}/logs/dts

spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS=true

# inspect redis
inspect.redis.mode=single
inspect.redis.sentinel.host=
inspect.redis.sentinel.name=redismaster

# ????
logback.defender.enable=true
logback.defender.replaces[0].regex=(adminPwd=)([^,}]*)
logback.defender.replaces[0].replacement=$1******
logback.defender.replaces[1].regex=("password":)([^,}]*)
logback.defender.replaces[1].replacement=$1"******"
logback.defender.replaces[2].regex=U1O5ZeRyLFd#u9T6TF9h
logback.defender.replaces[2].replacement=******
logback.defender.replaces[3].regex=(ZOOKEEPER_PASSWORD=)([^,}]*)
logback.defender.replaces[3].replacement=$1******

clusterInfoUnifyConfiguration=true
# detect
detect.amq.period=300000
detect.redis.period=300000

# schedule cron for test mail server
schedule.cron.test-mail-server=0 0 1 * * ?
# jwt
jwt.secret-key=472F4EA2D294EA50AEA66261A41115E22E337C35229AA5E73E4D95320AF820E2
jwt.timeout=7200s
# file access token
file.access.token.timeout=60s
noah.authorization=Vg1Sg0jVc3TtTYhgYpF1MBQMQwhf58vK

registry.manager.path=${base.dir}/image
# upgrade
sql.upgrade.goInception.port=4000
sql.upgrade.goInception.db-port=3307

user.operationLog.switch=true
#\u5BC6\u8BC4
application.security.strategy=none
application.security.ge.sign-url=/api/svs/bss/signData
application.security.ge.verify-signed-data-url=/api/svs/bss/verifySignedData
application.security.jit.sign-url=/mssgApi/dsvs/v1/pkcs1/ext/signData
application.security.jit.verify-signed-data-url=/mssgApi/dsvs/v1/pkcs1/verifyDataSign
application.security.jit.gen-Key-pair-url=/mssgApi/dsvs/v1/ext/genKeyPair
application.security.jit.create-key-url=/mssgApi/chiron/v1/system/createKey
application.security.jit.encrypt-url=/mssgApi/chiron/v1/system/encrypt
application.security.jit.decrypt-url=/mssgApi/chiron/v1/system/decrypt
#\u5E94\u7528\u4FE1\u606F
api.auth.clients.common_client=472F4EA2D294EA50AEA66261A41115E22E337C35229AA5E73E4D95320AF820E2
api.auth.clients.bm_server=5f3d8a7c1e9b2a6d4c0f8e7b5a9d3c2e1f6a8b7d4c3e2f1a0b9c8d7e6f5a4b3

#æ¯å¦å¼å®¹ä¸æ¹å®¹å¨äº
third.k8s.switch = false
