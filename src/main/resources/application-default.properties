# 默认环境配置（服务端环境）
# 该配置文件在没有指定Profile时使用，保持与原有application.properties相同的配置
# 确保服务端部署时的向后兼容性

# 服务端环境使用原有的配置值，保持不变
# 这样可以确保在服务端部署时不需要修改任何启动参数

# 基础路径配置 - 服务端默认值
base.dir=${manager.base.dir:/mnt/xylink}

# 脚本路径 - 服务端默认值
server.script.dir=/usr/libra/script

# 日志级别 - 服务端默认值
logging.level.com.xylink=info

# K8s配置 - 服务端默认值
k8s.protocol.default.http=true
k8s.config.path=${base.dir}/kubeconfig
k8s.prefix.url=http://127.0.0.1:8080

# Harbor配置 - 服务端默认值
harbor.prefix.url=http://hub.xylink.com:5000

# InfluxDB配置 - 服务端默认值
influxdb.url=http://127.0.0.1:8086

# 文件存储配置 - 服务端默认值
privatecloud.filestorage.localdir=/tmp
privatecloud.clientstorage.tmpdir=/tmp
privatecloud.script.dir=${base.dir}/private_manager/script

# 告警配置 - 服务端默认值
alert.check.initial.delay=1200000
alert.check.fixed.rate=600000
license.check.initial.delay=6000000
license.check.fixed.rate=600000

# 邮件测试配置 - 服务端默认值
schedule.cron.test-mail-server=0 0 1 * * ?

# 数据库备份配置 - 服务端默认值
db.backupdir=/usr/libra/mysqlbackup/

# 升级配置 - 服务端默认值
upgrade.storage.dir=${base.dir}/upgrade
upgrade.storage.log-folder=${base.dir}/upgrade/logs
upgrade.storage.log-compare=${base.dir}/upgrade/compare

# 日志配置 - 服务端默认值
main.log.dir=${base.dir}/logs/
client.log.root.path=${base.dir}/cfs/logserver/client/
hvv.log.root.path=${base.dir}/hvv/

# DTS配置 - 服务端默认值
dts.workspace=${base.dir}/logs/dts

# 镜像仓库配置 - 服务端默认值
registry.manager.path=${base.dir}/image

# 用户配置目录 - 服务端默认值
server.user.config.dir=/usr/

# 包根路径 - 服务端默认值
package.root.path=/home/<USER>

# FRP配置 - 服务端默认值
frp.file=/etc/privatefrpc/privatefrpc.ini

# OSS配置 - 服务端默认值
main.oss.path=${base.dir}/openresty/nginx_main/oss/logo

# 安全配置 - 服务端默认值
application.security.strategy=none

# 第三方K8s开关 - 服务端默认值
third.k8s.switch=false

# 集群信息统一配置 - 服务端默认值
clusterInfoUnifyConfiguration=true

# 检测配置 - 服务端默认值
detect.amq.period=300000
detect.redis.period=300000

# 用户操作日志开关 - 服务端默认值
user.operationLog.switch=true

# 验证码配置 - 服务端默认值
captcha.enableCaptcha=true

# 日志防护配置 - 服务端默认值
logback.defender.enable=true
