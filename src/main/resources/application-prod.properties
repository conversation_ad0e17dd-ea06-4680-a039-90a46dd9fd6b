# 生产环境配置
# 该配置文件在生产环境中使用，通过 --spring.profiles.active=prod 激活

# 生产环境基础路径配置
base.dir=${manager.base.dir:/mnt/xylink}

# 生产环境脚本路径
server.script.dir=/usr/libra/script

# 生产环境日志级别
logging.level.com.xylink=info
logging.level.root=warn

# 生产环境Redis配置
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.database=${REDIS_DATABASE:0}
spring.redis.password=${REDIS_PASSWORD:}

# 生产环境K8s配置
k8s.protocol.default.http=true
k8s.config.path=${base.dir}/kubeconfig
k8s.prefix.url=http://127.0.0.1:8080

# 生产环境Harbor配置
harbor.prefix.url=http://hub.xylink.com:5000

# 生产环境InfluxDB配置
influxdb.url=http://127.0.0.1:8086

# 生产环境文件存储配置
privatecloud.filestorage.localdir=${base.dir}/files
privatecloud.clientstorage.tmpdir=${base.dir}/tmp
privatecloud.script.dir=${base.dir}/private_manager/script

# 生产环境告警配置
alert.check.initial.delay=1200000
alert.check.fixed.rate=600000
license.check.initial.delay=6000000
license.check.fixed.rate=600000

# 生产环境邮件测试配置
schedule.cron.test-mail-server=0 0 1 * * ?

# 生产环境数据库备份配置
db.backupdir=/usr/libra/mysqlbackup/
db.host.backupdir=${base.dir}/db/mysqlbackup/
db.host.statis.backupdir=${base.dir}/db/statismysqlbackup/
db.host.surv.backupdir=${base.dir}/db/survmysqlbackup/
db.host.hbase.backupdir=${base.dir}/db/hbasebackup/
db.host.edu.backupdir=${base.dir}/db/edubackup/
db.host.webrtc.backupdir=${base.dir}/db/wertcbackup/
db.host.matrix.backupdir=${base.dir}/db/matrixbackup/
db.host.nightingale.backupdir=${base.dir}/db/nightingalebackup/

# 生产环境升级配置
upgrade.storage.dir=${base.dir}/upgrade
upgrade.storage.log-folder=${base.dir}/upgrade/logs
upgrade.storage.log-compare=${base.dir}/upgrade/compare

# 生产环境日志配置
main.log.dir=${base.dir}/logs/
client.log.root.path=${base.dir}/cfs/logserver/client/
hvv.log.root.path=${base.dir}/hvv/

# 生产环境DTS配置
dts.workspace=${base.dir}/logs/dts

# 生产环境镜像仓库配置
registry.manager.path=${base.dir}/image

# 生产环境用户配置目录
server.user.config.dir=/usr/

# 生产环境包根路径
package.root.path=/home/<USER>

# 生产环境FRP配置
frp.file=/etc/privatefrpc/privatefrpc.ini

# 生产环境OSS配置
main.oss.path=${base.dir}/openresty/nginx_main/oss/logo

# 生产环境安全配置
application.security.strategy=${SECURITY_STRATEGY:none}

# 生产环境第三方K8s开关
third.k8s.switch=${THIRD_K8S_SWITCH:false}

# 生产环境集群信息统一配置
clusterInfoUnifyConfiguration=true

# 生产环境检测配置
detect.amq.period=300000
detect.redis.period=300000

# 生产环境用户操作日志开关
user.operationLog.switch=true

# 生产环境验证码配置
captcha.enableCaptcha=true

# 生产环境日志防护配置
logback.defender.enable=true
