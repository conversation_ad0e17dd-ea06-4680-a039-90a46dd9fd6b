package com.xylink.manager.job.degradation;

import com.xylink.manager.job.config.JobConfigLoader;
import com.xylink.manager.job.model.JobExecutionHistory;
import com.xylink.manager.job.model.JobExecutionResult;
import com.xylink.manager.job.model.JobStatus;
import com.xylink.manager.job.service.InMemoryJobStatusService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * InMemoryJobStatusService功能单元测试
 * 测试内存状态服务的基本功能
 */
public class InMemoryJobStatusServiceTest {

    private InMemoryJobStatusService inMemoryJobStatusService;

    @Mock
    private JobConfigLoader jobConfigLoader;a

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        inMemoryJobStatusService = new InMemoryJobStatusService();
        // 使用反射设置jobConfigLoader
        try {
            java.lang.reflect.Field field = InMemoryJobStatusService.class.getDeclaredField("jobConfigLoader");
            field.setAccessible(true);
            field.set(inMemoryJobStatusService, jobConfigLoader);
        } catch (Exception e) {
            // 如果设置失败，某些测试可能会受影响，但基本功能测试仍然可以进行
        }
    }

    @Test
    public void testRecordJobStartAndCompletion() {
        // 测试记录Job开始和完成
        String jobName = "test-job-lifecycle";
        String executionId = "exec-" + System.currentTimeMillis();
        
        // 记录Job开始
        inMemoryJobStatusService.recordJobStart(jobName, executionId);
        
        // 验证Job状态
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        assertNotNull(status, "Job状态不应该为null");
        assertEquals(jobName, status.getJobName(), "Job名称应该匹配");
        assertEquals(executionId, status.getLastExecutionId(), "执行ID应该匹配");
        assertTrue(status.isRunning(), "Job应该处于运行状态");
        assertNotNull(status.getLastTriggerTime(), "最后触发时间不应该为null");
        
        // 模拟Job完成
        JobExecutionResult result = JobExecutionResult.success(1500L, "测试完成");
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
        
        // 验证完成状态
        status = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(status.isRunning(), "Job应该不再运行");
        assertEquals("SUCCESS", status.getLastStatus(), "状态应该为SUCCESS");
        assertEquals(1500L, status.getLastDurationMs(), "执行时长应该匹配");
        assertNotNull(status.getLastFinishTime(), "完成时间不应该为null");
        assertNotNull(status.getLastSuccessTime(), "最后成功时间不应该为null");
        assertEquals(0, status.getConsecutiveFailCount().intValue(), "连续失败次数应该为0");
        
        System.out.println("✅ Job生命周期记录测试通过");
    }

    @Test
    public void testRecordJobFailure() {
        // 测试记录Job失败
        String jobName = "test-job-failure";
        String executionId = "exec-fail-" + System.currentTimeMillis();
        
        // 记录Job开始
        inMemoryJobStatusService.recordJobStart(jobName, executionId);
        
        // 模拟Job失败
        JobExecutionResult result = JobExecutionResult.failure(2000L, "测试失败", "连接超时", 0);
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
        
        // 验证失败状态
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        assertFalse(status.isRunning(), "Job应该不再运行");
        assertEquals("FAIL", status.getLastStatus(), "状态应该为FAIL");
        assertEquals(2000L, status.getLastDurationMs(), "执行时长应该匹配");
        assertEquals(1, status.getConsecutiveFailCount().intValue(), "连续失败次数应该为1");
        
        // 再次失败
        String executionId2 = "exec-fail-2-" + System.currentTimeMillis();
        inMemoryJobStatusService.recordJobStart(jobName, executionId2);
        JobExecutionResult result2 = JobExecutionResult.failure(1800L, "再次失败", "网络错误", 1);
        inMemoryJobStatusService.recordJobCompletion(jobName, executionId2, result2);
        
        // 验证连续失败计数
        status = inMemoryJobStatusService.getJobStatus(jobName);
        assertEquals(2, status.getConsecutiveFailCount().intValue(), "连续失败次数应该为2");
        
        System.out.println("✅ Job失败记录测试通过");
    }

    @Test
    public void testJobExecutionHistory() {
        // 测试Job执行历史记录
        String jobName = "test-job-history";
        
        // 执行多次Job
        for (int i = 1; i <= 5; i++) {
            String executionId = "exec-history-" + i;
            
            inMemoryJobStatusService.recordJobStart(jobName, executionId);
            
            JobExecutionResult result;
            if (i % 2 == 0) {
                result = JobExecutionResult.failure(1000L + i * 100, "失败" + i, "错误" + i, 0);
            } else {
                result = JobExecutionResult.success(1000L + i * 100, "成功" + i);
            }
            
            inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
        }
        
        // 获取执行历史
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
        
        // 验证历史记录
        assertNotNull(history, "执行历史不应该为null");
        assertEquals(5, history.size(), "应该有5条历史记录");
        
        // 验证记录顺序（最新的在前面）
        JobExecutionHistory latestRecord = history.get(0);
        assertEquals("exec-history-5", latestRecord.getExecutionId(), "最新记录应该是第5次执行");
        assertEquals("SUCCESS", latestRecord.getStatus(), "最新记录状态应该为SUCCESS");
        
        JobExecutionHistory oldestRecord = history.get(4);
        assertEquals("exec-history-1", oldestRecord.getExecutionId(), "最旧记录应该是第1次执行");
        
        // 测试分页
        List<JobExecutionHistory> page1 = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 3);
        assertEquals(3, page1.size(), "第一页应该有3条记录");
        
        List<JobExecutionHistory> page2 = inMemoryJobStatusService.getJobExecutionHistory(jobName, 1, 3);
        assertEquals(2, page2.size(), "第二页应该有2条记录");
        
        System.out.println("✅ Job执行历史测试通过");
    }

    @Test
    public void testMultipleJobsStatus() {
        // 测试多个Job的状态管理
        String[] jobNames = {"job-1", "job-2", "job-3"};
        
        // 为每个Job记录不同的状态
        for (int i = 0; i < jobNames.length; i++) {
            String jobName = jobNames[i];
            String executionId = "exec-" + jobName + "-" + System.currentTimeMillis();
            
            inMemoryJobStatusService.recordJobStart(jobName, executionId);
            
            if (i == 0) {
                // job-1: 成功完成
                JobExecutionResult result = JobExecutionResult.success(1000L, "成功");
                inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
            } else if (i == 1) {
                // job-2: 失败
                JobExecutionResult result = JobExecutionResult.failure(2000L, "失败", "错误", 0);
                inMemoryJobStatusService.recordJobCompletion(jobName, executionId, result);
            }
            // job-3: 保持运行状态
        }
        
        // 验证各个Job的状态
        JobStatus status1 = inMemoryJobStatusService.getJobStatus("job-1");
        assertFalse(status1.isRunning(), "job-1应该不在运行");
        assertEquals("SUCCESS", status1.getLastStatus(), "job-1状态应该为SUCCESS");
        
        JobStatus status2 = inMemoryJobStatusService.getJobStatus("job-2");
        assertFalse(status2.isRunning(), "job-2应该不在运行");
        assertEquals("FAIL", status2.getLastStatus(), "job-2状态应该为FAIL");
        
        JobStatus status3 = inMemoryJobStatusService.getJobStatus("job-3");
        assertTrue(status3.isRunning(), "job-3应该在运行");
        
        System.out.println("✅ 多Job状态管理测试通过");
    }

    @Test
    public void testStatistics() {
        // 测试统计功能
        
        // 初始统计
        Map<String, Object> initialStats = inMemoryJobStatusService.getStatistics();
        assertNotNull(initialStats, "统计信息不应该为null");
        assertTrue(initialStats.containsKey("totalJobs"), "应该包含总任务数");
        assertTrue(initialStats.containsKey("runningJobs"), "应该包含运行中任务数");
        assertTrue(initialStats.containsKey("totalHistoryRecords"), "应该包含历史记录总数");
        
        // 添加一些数据
        String jobName1 = "stats-job-1";
        String jobName2 = "stats-job-2";
        
        // job1: 完成
        inMemoryJobStatusService.recordJobStart(jobName1, "exec-1");
        JobExecutionResult result1 = JobExecutionResult.success(1000L, "完成");
        inMemoryJobStatusService.recordJobCompletion(jobName1, "exec-1", result1);
        
        // job2: 运行中
        inMemoryJobStatusService.recordJobStart(jobName2, "exec-2");
        
        // 获取更新后的统计
        Map<String, Object> updatedStats = inMemoryJobStatusService.getStatistics();
        
        int totalJobs = (Integer) updatedStats.get("totalJobs");
        int runningJobs = (Integer) updatedStats.get("runningJobs");
        int totalHistoryRecords = (Integer) updatedStats.get("totalHistoryRecords");
        
        assertTrue(totalJobs >= 2, "总任务数应该至少为2");
        assertTrue(runningJobs >= 1, "运行中任务数应该至少为1");
        assertTrue(totalHistoryRecords >= 1, "历史记录总数应该至少为1");
        
        System.out.println("✅ 统计功能测试通过");
        System.out.println("   - 总任务数: " + totalJobs);
        System.out.println("   - 运行中任务数: " + runningJobs);
        System.out.println("   - 历史记录总数: " + totalHistoryRecords);
    }

    @Test
    public void testClearAllData() {
        // 测试清理所有数据功能
        
        // 添加一些测试数据
        String jobName = "test-clear-data";
        inMemoryJobStatusService.recordJobStart(jobName, "exec-clear");
        JobExecutionResult result = JobExecutionResult.success(1000L, "测试");
        inMemoryJobStatusService.recordJobCompletion(jobName, "exec-clear", result);
        
        // 验证数据存在
        JobStatus status = inMemoryJobStatusService.getJobStatus(jobName);
        assertNotNull(status, "状态应该存在");
        
        List<JobExecutionHistory> history = inMemoryJobStatusService.getJobExecutionHistory(jobName, 0, 10);
        assertFalse(history.isEmpty(), "历史记录应该存在");
        
        // 清理所有数据
        inMemoryJobStatusService.clearAllData();
        
        // 验证数据已清理
        Map<String, Object> stats = inMemoryJobStatusService.getStatistics();
        assertEquals(0, stats.get("totalJobs"), "总任务数应该为0");
        assertEquals(0, stats.get("runningJobs"), "运行中任务数应该为0");
        assertEquals(0, stats.get("totalHistoryRecords"), "历史记录总数应该为0");
        
        System.out.println("✅ 清理所有数据测试通过");
    }

    @Test
    public void testInMemoryStatusServiceComprehensive() {
        // 综合测试内存状态服务的所有功能
        System.out.println("=== InMemoryJobStatusService综合功能测试 ===");
        
        // 1. Job生命周期测试
        testRecordJobStartAndCompletion();
        
        // 2. Job失败记录测试
        testRecordJobFailure();
        
        // 3. 执行历史测试
        testJobExecutionHistory();
        
        // 4. 多Job状态管理测试
        testMultipleJobsStatus();
        
        // 5. 统计功能测试
        testStatistics();
        
        // 6. 数据清理测试
        testClearAllData();
        
        System.out.println("✅ InMemoryJobStatusService综合功能测试全部通过");
        System.out.println("📋 测试结果:");
        System.out.println("   - Job生命周期管理: ✅");
        System.out.println("   - 失败状态记录: ✅");
        System.out.println("   - 执行历史管理: ✅");
        System.out.println("   - 多Job状态管理: ✅");
        System.out.println("   - 统计功能: ✅");
        System.out.println("   - 数据清理: ✅");
        System.out.println("🎯 结论: 内存状态服务功能完整，可以作为Redis状态服务的降级替代方案");
    }
}
