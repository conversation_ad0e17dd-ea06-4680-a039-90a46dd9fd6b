# Spring Profile 配置指南

本项目已支持基于Spring Profile的多环境配置管理，可以轻松在本地开发环境和生产环境之间切换配置。

## 📁 配置文件结构

```
src/main/resources/
├── application.properties          # 主配置文件（共同配置）
├── application-local.properties    # 本地开发环境配置
├── application-prod.properties     # 生产环境配置
└── application-default.properties  # 默认环境配置（服务端兼容）
```

## 🚀 启动方式

### 1. 本地开发环境

#### 方式一：使用启动脚本（推荐）
```bash
# Linux/macOS
./start-local.sh

# Windows
start-local.bat
```

#### 方式二：使用Gradle命令
```bash
# 指定profile参数
./gradlew bootRun -Pprofile=local

# 或使用系统属性
./gradlew bootRun -Dspring.profiles.active=local
```

#### 方式三：IDE启动
在IDE中设置VM参数：
```
-Dspring.profiles.active=local
```

### 2. 生产环境

#### 使用启动脚本
```bash
./start-prod.sh
```

#### 使用Gradle命令
```bash
./gradlew bootRun -Pprofile=prod
```

#### 使用JAR包启动
```bash
java -jar manager.war --spring.profiles.active=prod
```

### 3. 服务端环境（保持现状）

服务端部署时无需修改任何启动参数，会自动使用默认配置：
```bash
# 原有的启动方式保持不变
java -jar manager.war
```

## ⚙️ 配置说明

### 本地开发环境 (local)
- **Redis**: localhost:6379
- **日志级别**: debug
- **基础路径**: /tmp/xylink
- **脚本路径**: 用户工作目录
- **K8s配置**: 使用本地kubeconfig
- **告警间隔**: 更短的检查间隔用于测试

### 生产环境 (prod)
- **Redis**: 支持环境变量配置
- **日志级别**: info/warn
- **基础路径**: /mnt/xylink
- **脚本路径**: /usr/libra/script
- **完整的生产环境路径配置**

### 默认环境 (default)
- **保持与原有application.properties相同的配置**
- **确保服务端部署的向后兼容性**

## 🔧 自定义配置

### 添加新的Profile
1. 创建新的配置文件：`application-{profile}.properties`
2. 添加特定环境的配置
3. 使用 `--spring.profiles.active={profile}` 启动

### 环境变量支持
生产环境配置支持环境变量，例如：
```properties
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=${REDIS_PASSWORD:}
```

### 配置优先级
Spring Boot配置加载优先级（从高到低）：
1. 命令行参数
2. 系统属性
3. application-{profile}.properties
4. application.properties

## 📝 最佳实践

### 1. 敏感信息管理
- 生产环境敏感信息使用环境变量
- 本地开发使用默认值或测试值
- 不要在配置文件中硬编码密码

### 2. 路径配置
- 使用相对路径和环境变量
- 本地开发使用临时目录
- 生产环境使用标准路径

### 3. 日志配置
- 本地开发使用debug级别便于调试
- 生产环境使用info/warn级别减少日志量

### 4. 数据库配置
- 本地开发连接本地数据库
- 生产环境使用环境变量配置连接信息

## 🔍 故障排除

### 查看当前激活的Profile
启动时会在控制台输出当前使用的Profile信息。

### 配置不生效
1. 检查Profile名称是否正确
2. 确认配置文件路径
3. 查看启动日志中的配置加载信息

### 服务端部署问题
如果服务端部署出现问题，可以显式指定default profile：
```bash
java -jar manager.war --spring.profiles.active=default
```

## 📚 相关文档

- [Spring Boot Profiles官方文档](https://docs.spring.io/spring-boot/docs/current/reference/html/features.html#features.profiles)
- [Spring Boot外部化配置](https://docs.spring.io/spring-boot/docs/current/reference/html/features.html#features.external-config)

## 🎯 总结

通过Profile配置，您可以：
- ✅ 本地开发使用 `local` profile，配置适合开发的参数
- ✅ 生产环境使用 `prod` profile，配置生产级别的参数  
- ✅ 服务端部署保持现状不变，确保向后兼容
- ✅ 轻松在不同环境间切换，无需修改代码
- ✅ 支持环境变量，提高配置的灵活性
