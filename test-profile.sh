#!/bin/bash

# Profile配置测试脚本
# 验证不同Profile下的配置是否正确加载

echo "=== Spring Profile 配置测试 ==="
echo ""

# 测试local profile
echo "1. 测试 local profile 配置..."
echo "启动命令: ./gradlew bootRun -Pprofile=local --dry-run"
./gradlew bootRun -Pprofile=local --dry-run | grep "Profile:"
echo ""

# 测试prod profile  
echo "2. 测试 prod profile 配置..."
echo "启动命令: ./gradlew bootRun -Pprofile=prod --dry-run"
./gradlew bootRun -Pprofile=prod --dry-run | grep "Profile:"
echo ""

# 测试default profile
echo "3. 测试 default profile 配置..."
echo "启动命令: ./gradlew bootRun --dry-run"
./gradlew bootRun --dry-run | grep "Profile:"
echo ""

echo "=== 配置文件检查 ==="
echo ""

# 检查配置文件是否存在
echo "检查配置文件:"
ls -la src/main/resources/application*.properties

echo ""
echo "=== 测试完成 ==="
echo ""
echo "如果看到正确的Profile输出，说明配置成功！"
echo "现在可以使用以下命令启动应用："
echo "  本地开发: ./start-local.sh"
echo "  生产环境: ./start-prod.sh"
echo "  默认环境: ./gradlew bootRun"
