#!/bin/bash

# 生产环境启动脚本
# 使用prod profile启动Spring Boot应用

echo "=== 启动生产环境 ==="
echo "Profile: prod"
echo "时间: $(date)"
echo "====================="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保已安装Java 8或更高版本"
    exit 1
fi

# 显示Java版本
echo "Java版本:"
java -version

echo ""
echo "正在启动应用..."
echo ""

# 使用Gradle启动，指定prod profile
./gradlew bootRun -Pprofile=prod

echo ""
echo "应用已停止"
