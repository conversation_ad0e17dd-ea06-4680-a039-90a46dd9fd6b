buildscript {
    ext {
        springBootVersion = '2.7.18'
    }
    repositories {
        maven {
            url = 'http://maven.xylink.com:8081/nexus/content/groups/public/'
            allowInsecureProtocol = true
        }
        maven {
            url = 'https://maven.aliyun.com/nexus/content/groups/public/'
        }
        mavenLocal()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }
}

apply plugin: 'java'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'war'

java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

repositories {
    maven {
        url = 'http://maven.xylink.com:8081/nexus/content/groups/public/'
        allowInsecureProtocol = true
    }
    maven {
        url = 'https://maven.aliyun.com/nexus/content/groups/public/'
    }
    mavenLocal()
}

war {
    baseName = 'manager'
}

configurations {
    providedRuntime
}

dependencies {
    implementation('org.springframework.boot:spring-boot-starter-security')
    implementation('org.springframework.boot:spring-boot-starter-web')
    implementation('org.apache.tomcat.embed:tomcat-embed-core:9.0.98')
    implementation('org.apache.tomcat.embed:tomcat-embed-el:9.0.98')
    implementation('org.apache.tomcat.embed:tomcat-embed-websocket:9.0.98')
    implementation('org.apache.tomcat:tomcat-annotations-api:9.0.98')

    implementation('org.springframework.boot:spring-boot-starter-data-redis')
    implementation('org.springframework.boot:spring-boot-starter-data-elasticsearch')
    // 使用Redisson作为统一Redis客户端，提供更好的性能和分布式功能
    implementation('org.redisson:redisson-spring-boot-starter:3.17.7')
    // 移除单独的Jedis依赖，Redisson已提供完整功能
    // implementation("redis.clients:jedis")

    implementation('org.springframework.boot:spring-boot-starter-aop')
    implementation('org.springframework.boot:spring-boot-starter-validation')
    implementation('org.springframework.boot:spring-boot-starter-mail')
    implementation("org.springframework.boot:spring-boot-starter-freemarker")
    implementation("org.springframework.boot:spring-boot-starter-test")
    implementation 'org.springframework.security:spring-security-config'
    implementation("org.springframework.security:spring-security-web")
    implementation("org.springframework.security:spring-security-core")
    implementation("org.springframework.security:spring-security-crypto")
    implementation("org.springframework.security:spring-security-acl")
    implementation("org.springframework.security:spring-security-taglibs")

    implementation 'net.lingala.zip4j:zip4j:2.11.5'

    implementation("org.apache.httpcomponents:httpclient:4.5.14")
    implementation("com.fasterxml.jackson.module:jackson-module-jaxb-annotations")

    //kuber
    implementation ('io.fabric8:kubernetes-client:5.10.2'){
        exclude group: "com.squareup.okhttp"
        exclude group: "org.bouncycastle"
    }
    implementation ('com.squareup.okhttp3:okhttp:4.12.0')
    implementation ('com.squareup.okhttp3:logging-interceptor:4.12.0')

    //influxdb
    implementation ('org.influxdb:influxdb-java')

    //cmd
    implementation("org.codehaus.plexus:plexus-utils:3.0.24")
    implementation ('com.imaginatelabs:shellbert:1.0.0')
    
    implementation('net.jodah:expiringmap:0.5.8')
    implementation ('org.apache.commons:commons-compress:1.26.0')
    implementation("org.apache.commons:commons-lang3:3.17.0")

    //lombok
    implementation 'org.projectlombok:lombok'
    testImplementation 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    //mapstruct
    implementation 'org.mapstruct:mapstruct:1.4.2.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.4.2.Final'

    //google guava util
    implementation ('com.google.guava:guava')

    implementation ('com.github.rholder:guava-retrying:2.0.0') {
        exclude group: "com.google.code.findbugs",  module: "jsr305"
    }

    //mysql
    implementation('mysql:mysql-connector-java:8.0.33')
    implementation 'com.oceanbase:oceanbase-client:2.4.1'

    implementation 'commons-codec:commons-codec'

    implementation('org.apache.kafka:kafka-clients:3.6.1')

    implementation 'org.jsoup:jsoup:1.17.2'
    implementation ('com.qcloud:cos_api:5.6.89') {
        exclude group: "com.squareup.okhttp"
        exclude group: "org.bouncycastle"
    }
    implementation 'org.bouncycastle:bcprov-jdk18on:1.78.1'
    implementation("org.bouncycastle:bcpkix-jdk18on:1.78.1")

    implementation 'com.amazonaws:aws-java-sdk-s3:1.12.767'

    implementation('org.apache.curator:curator-framework:5.5.0'){
        exclude group: "log4j"
    }

    // jasypt
    implementation ('com.github.ulisesbocchio:jasypt-spring-boot-starter'){
        exclude group: "org.jasypt"
    }
    implementation('org.jasypt:jasypt')

    implementation 'net.coobird:thumbnailator:0.4.11'
    // 移除重复的Jedis依赖，已统一使用Redisson
    // implementation 'redis.clients:jedis'

    implementation 'net.minidev:json-smart:2.5.2'
    implementation 'org.json:json:20250107'
    implementation 'io.netty:netty-handler:4.1.118.Final'
    implementation 'org.apache.zookeeper:zookeeper:3.9.2'
    implementation 'org.yaml:snakeyaml'
    implementation 'com.jayway.jsonpath:json-path'
    implementation 'com.huawei.opengauss:opengauss-jdbc:3.0.0-r6'
    // 神通
    implementation('com.shentong:oscarJDBC')
    // 达梦
    implementation 'com.dameng:DmJdbcDriver18'
    // 金仓数据库
    implementation 'com.ainemo.jdbc:kingbase8'

    //6.0巡检合入添加依赖
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'org.mybatis:mybatis-spring:2.0.7'
    implementation 'com.zaxxer:HikariCP'
    implementation ('net.sf.jasperreports:jasperreports:6.21.5') {
        exclude group: "com.lowagie", module: "itext"
        exclude group: "com.fasterxml.jackson.dataformat", module: "jackson-dataformat-xml"
    }

    // excel
    implementation 'com.alibaba:easyexcel:4.0.3'
    implementation('org.apache.poi:poi:5.4.0')
    implementation('org.apache.poi:poi-ooxml:5.4.0'){
        exclude group: "org.apache.commons", module: "commons-compress"
    }

    //6.0安全组合入cms 1000
    implementation 'com.baomidou:mybatis-plus:3.5.9'
    implementation 'com.baomidou:mybatis-plus-core:3.5.9'
    implementation 'com.baomidou:mybatis-plus-annotation:3.5.9'
    implementation 'com.baomidou:mybatis-plus-extension:3.5.9'

    //漏洞修复
    implementation("org.eclipse.parsson:parsson:1.0.5")
    implementation("org.elasticsearch:elasticsearch:7.17.23")
    implementation("org.xerial.snappy:snappy-java:********")
    implementation("org.springframework:spring-expression:5.3.39")
    implementation("com.squareup.okio:okio:3.9.1")
    implementation("org.xmlunit:xmlunit-core:2.10.0")
    implementation("com.opencsv:opencsv:5.7.1")

    //替换hutool
    implementation("io.jsonwebtoken:jjwt-api:0.12.6")
    implementation("io.jsonwebtoken:jjwt-impl:0.12.6")
    implementation("io.jsonwebtoken:jjwt-jackson:0.12.6")

    implementation("org.codehaus.janino:janino")
    
    // JSON日志格式化（用于Job执行日志）
    implementation 'net.logstash.logback:logstash-logback-encoder:7.2'
    
    bootRun {
        jvmArgs("-Dspring.config.location=file://"+projectDir.path+"/src/main/resources/application.properties")
        println "boot jvm: -Dspring.config.location=file://"+projectDir.path+"/src/main/resources/application.properties"
    }

    dependencyManagement {
        imports{
            mavenBom 'com.xylink:dependencies-bom-5.2:2.7.18-20250926-SNAPSHOT'
            mavenBom "org.springframework:spring-framework-bom:5.3.34"
            mavenBom 'com.fasterxml.jackson:jackson-bom:2.16.0'
        }
    }
}

test {
    useJUnitPlatform()
}
